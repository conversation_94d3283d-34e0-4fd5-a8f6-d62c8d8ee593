// 项目经理测试用例集合
// 文件位置: tools/ace/src/web_interface/static/debug/project_manager_tests.js
// 遵循通用测试架构规范：JS驱动，通过UniversalDebugger动态调用

/**
 * 测试项目经理状态发送功能
 * 通过UniversalDebugger动态调用ProjectManager，测试WebSocket状态发送
 */
function testProjectManagerStatusSending() {
    addDebugLogEntry('INFO', 'PROJECT_MANAGER_TEST', '🧪 开始测试项目经理状态发送功能');
    
    const testData = {
        task_id: "test_pm_status_sending_" + Date.now(),
        executor_role: "项目经理状态测试器",
        task_context: {
            "package_info": {
                "package_name": "project_manager_v2",
                "module_name": "manager.project_manager",
                "class_name": "ProjectManager"
            },
            "design_doc_path": "docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/1-总体架构设计-V2.md",
            "instance_mode": "create_new"
        },
        execution_params: {
            original_content: "测试项目经理状态发送功能",
            pycrud_operations: ["STATUS_SEND_TEST"],
            guardrails: {
                "安全边界": {
                    "WebSocket安全": "只能发送测试状态，不能执行实际业务逻辑"
                }
            },
            constraints: {
                "测试要求": {
                    "状态类型": "必须包含多种状态类型进行测试",
                    "消息格式": "必须符合UI标准消息格式",
                    "控制台输出": "前端必须能接收到状态并在控制台显示"
                }
            },
            context: {
                "test_mode": true,
                "websocket_test": true,
                "status_types": ["workspace_created", "review_stage_progress", "review_stage_completed", "review_completed"]
            },
            confidence_threshold: 0.85
        }
    };
    
    executeProjectManagerTest(testData, 'testProjectManagerStatusSending');
}

/**
 * 测试项目经理WebSocket连接功能
 */
function testProjectManagerWebSocketConnection() {
    addDebugLogEntry('INFO', 'PROJECT_MANAGER_TEST', '🔌 测试项目经理WebSocket连接功能');
    
    const testData = {
        task_id: "test_pm_websocket_" + Date.now(),
        executor_role: "项目经理WebSocket测试器",
        task_context: {
            "package_info": {
                "package_name": "project_manager_v2",
                "module_name": "manager.project_manager",
                "class_name": "ProjectManager"
            },
            "design_doc_path": "docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/1-总体架构设计-V2.md",
            "instance_mode": "create_new"
        },
        execution_params: {
            original_content: "测试项目经理WebSocket连接和消息发送",
            pycrud_operations: ["WEBSOCKET_CONNECTION_TEST"],
            guardrails: {
                "连接安全": {
                    "认证": "必须验证WebSocket连接认证",
                    "心跳": "必须支持心跳机制"
                }
            },
            constraints: {
                "连接要求": {
                    "命名空间": "必须使用 /ws/pm-v2 命名空间",
                    "消息格式": "必须符合标准消息格式",
                    "断线重连": "必须支持断线重连机制"
                }
            },
            context: {
                "test_mode": true,
                "websocket_test": true,
                "connection_test": true
            },
            confidence_threshold: 0.90
        }
    };
    
    executeProjectManagerTest(testData, 'testProjectManagerWebSocketConnection');
}

/**
 * 测试项目经理审查流程状态发送
 */
function testProjectManagerReviewStatusSending() {
    addDebugLogEntry('INFO', 'PROJECT_MANAGER_TEST', '📊 测试项目经理审查流程状态发送');
    
    const testData = {
        task_id: "test_pm_review_status_" + Date.now(),
        executor_role: "项目经理审查状态测试器",
        task_context: {
            "package_info": {
                "package_name": "project_manager_v2",
                "module_name": "manager.project_manager",
                "class_name": "ProjectManager"
            },
            "design_doc_path": "docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/1-总体架构设计-V2.md",
            "instance_mode": "create_new"
        },
        execution_params: {
            original_content: "测试项目经理审查流程的状态发送功能",
            pycrud_operations: ["REVIEW_STATUS_TEST"],
            guardrails: {
                "审查安全": {
                    "阶段验证": "只能发送测试状态，不能执行实际审查"
                }
            },
            constraints: {
                "审查状态要求": {
                    "四阶段": "必须测试四个审查阶段的状态发送",
                    "进度计算": "必须正确计算进度百分比",
                    "状态指示器": "必须包含正确的状态指示器"
                }
            },
            context: {
                "test_mode": true,
                "review_test": true,
                "stages": ["asset_inventory", "syntax_validation", "architecture_assessment", "completeness_audit"]
            },
            confidence_threshold: 0.88
        }
    };
    
    executeProjectManagerTest(testData, 'testProjectManagerReviewStatusSending');
}

/**
 * 通用的项目经理测试执行函数
 * 通过UniversalDebugger调用ProjectManager进行测试
 */
function executeProjectManagerTest(testData, testName) {
    addDebugLogEntry('INFO', 'PROJECT_MANAGER_TEST', `🚀 开始执行项目经理测试: ${testName}`);
    
    // 记录测试参数
    const packageInfo = testData.task_context.package_info;
    const instanceMode = testData.task_context.instance_mode || 'create_new';
    addDebugLogEntry('INFO', 'PACKAGE_INFO', `使用包: ${packageInfo.package_name}.${packageInfo.module_name}.${packageInfo.class_name}`);
    addDebugLogEntry('INFO', 'INSTANCE_MODE', `实例模式: ${instanceMode}`);
    addDebugLogEntry('INFO', 'TEST_CONTEXT', `测试上下文: ${JSON.stringify(testData.execution_params.context)}`);
    
    // 设置WebSocket消息监听器（如果测试涉及WebSocket）
    if (testData.execution_params.context && testData.execution_params.context.websocket_test) {
        setupWebSocketTestListener(testName);
    }
    
    // 返回Promise，支持异步等待
    return new Promise((resolve, reject) => {
        // 调用UniversalDebugger API
        fetch('/api/universal_debugger', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addDebugLogEntry('SUCCESS', 'PROJECT_MANAGER_TEST', `✅ ${testName} 执行成功`);
                addDebugLogEntry('INFO', 'EXECUTION_RESULT', `置信度: ${data.confidence || 'N/A'}`);
                addDebugLogEntry('INFO', 'EXECUTION_TIME', `执行时间: ${data.execution_time || 'N/A'}秒`);
                
                // 如果返回了状态发送信息
                if (data.status_sent) {
                    addDebugLogEntry('INFO', 'STATUS_SENT', `状态发送成功: ${data.status_sent}`);
                }
                
                // 如果返回了WebSocket连接信息
                if (data.websocket_info) {
                    addDebugLogEntry('INFO', 'WEBSOCKET_INFO', `WebSocket信息: ${JSON.stringify(data.websocket_info)}`);
                }
                
                resolve(data);
                // 显示成功 Toast
                if (typeof showToast === 'function') {
                    showToast(`✅ ${testName} 执行成功`);
                }
            } else {
                const errorMsg = data.error || data.error_message || '未知错误';
                addDebugLogEntry('ERROR', 'PROJECT_MANAGER_TEST', `❌ ${testName} 执行失败: ${errorMsg}`);
                // 显示失败 Toast
                if (typeof showToast === 'function') {
                    showToast(`❌ ${testName} 执行失败: ${errorMsg}`);
                }
                reject(new Error(errorMsg));
            }
        })
        .catch(error => {
            const errorMsg = `${testName} 网络错误: ${error.message}`;
            addDebugLogEntry('ERROR', 'PROJECT_MANAGER_TEST', `❌ ${errorMsg}`);
            // 显示失败 Toast
            if (typeof showToast === 'function') {
                showToast(`❌ ${testName} 网络错误: ${error.message}`);
            }
            reject(new Error(errorMsg));
        });
    });
}

/**
 * 设置WebSocket测试监听器
 * 监听项目经理发送的状态消息
 */
function setupWebSocketTestListener(testName) {
    addDebugLogEntry('INFO', 'WEBSOCKET_LISTENER', `🔍 设置WebSocket监听器: ${testName}`);
    
    // 检查是否已有socket连接
    if (typeof socket !== 'undefined' && socket) {
        // 监听项目经理相关的WebSocket事件
        const events = [
            'workspace_created',
            'review_stage_progress',
            'review_stage_completed',
            'review_completed',
            'admission_review_started',
            'review_failed',
            'status_update_on_reconnect'
        ];
        
        events.forEach(eventType => {
            socket.on(eventType, (data) => {
                console.log(`📨 [${testName}] 收到WebSocket消息: ${eventType}`, data);
                addDebugLogEntry('INFO', 'WEBSOCKET_RECEIVED', `📨 收到消息: ${eventType}`);
                addDebugLogEntry('INFO', 'MESSAGE_DATA', `消息数据: ${JSON.stringify(data)}`);
            });
        });
        
        addDebugLogEntry('SUCCESS', 'WEBSOCKET_LISTENER', `✅ WebSocket监听器设置完成，监听事件: ${events.join(', ')}`);
    } else {
        addDebugLogEntry('WARNING', 'WEBSOCKET_LISTENER', `⚠️ WebSocket连接不可用，无法设置监听器`);
    }
}

/**
 * 清理WebSocket测试监听器
 */
function cleanupWebSocketTestListener() {
    if (typeof socket !== 'undefined' && socket) {
        const events = [
            'workspace_created',
            'review_stage_progress',
            'review_stage_completed',
            'review_completed',
            'admission_review_started',
            'review_failed',
            'status_update_on_reconnect'
        ];
        
        events.forEach(eventType => {
            socket.off(eventType);
        });
        
        addDebugLogEntry('INFO', 'WEBSOCKET_CLEANUP', `🧹 WebSocket监听器已清理`);
    }
}

/**
 * 项目经理测试套件 - 一键执行所有测试
 */
function runProjectManagerTestSuite() {
    addDebugLogEntry('INFO', 'TEST_SUITE', '🎯 开始执行项目经理测试套件');
    
    const tests = [
        { name: 'testProjectManagerStatusSending', func: testProjectManagerStatusSending },
        { name: 'testProjectManagerWebSocketConnection', func: testProjectManagerWebSocketConnection },
        { name: 'testProjectManagerReviewStatusSending', func: testProjectManagerReviewStatusSending }
    ];
    
    let completedTests = 0;
    let successfulTests = 0;
    
    tests.forEach((test, index) => {
        setTimeout(() => {
            test.func()
                .then(() => {
                    successfulTests++;
                    addDebugLogEntry('SUCCESS', 'TEST_SUITE', `✅ ${test.name} 完成`);
                })
                .catch((error) => {
                    addDebugLogEntry('ERROR', 'TEST_SUITE', `❌ ${test.name} 失败: ${error.message}`);
                })
                .finally(() => {
                    completedTests++;
                    if (completedTests === tests.length) {
                        addDebugLogEntry('INFO', 'TEST_SUITE', `📊 测试套件完成: ${successfulTests}/${tests.length} 成功`);
                        cleanupWebSocketTestListener();
                    }
                });
        }, index * 2000); // 每个测试间隔2秒
    });
} 